{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\unifiedcompanymanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\unifiedcompanymanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efuserdevicedal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efuserdevicedal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efcompanyuserdal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efcompanyuserdal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efworkoutprogramtemplatedal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efworkoutprogramtemplatedal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efworkoutprogramexercisedal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efworkoutprogramexercisedal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efuseroperationclaimdal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efuseroperationclaimdal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efworkoutprogramdaydal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efworkoutprogramdaydal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\gymcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\gymcontext.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\entities.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\entities.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efusercompanydal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efusercompanydal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efcompanyexercisedal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efcompanyexercisedal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efcompanydal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efcompanydal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\webapi\\controllers\\companycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|solutionrelative:webapi\\controllers\\companycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\debtpaymentmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\debtpaymentmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\companyusermanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\companyusermanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\companymanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\companymanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\companyexercisemanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\companyexercisemanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\companyadressmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\companyadressmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\webapi\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|solutionrelative:webapi\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efmemberworkoutprogramdal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efmemberworkoutprogramdal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\memberworkoutprogrammanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\memberworkoutprogrammanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\webapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|solutionrelative:webapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\membermanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\membermanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\entities\\dtos\\systemexercisedto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{5ADE3D9C-DA0E-4771-AB41-FE9437640F54}|Entities\\Entities.csproj|solutionrelative:entities\\dtos\\systemexercisedto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\webapi\\controllers\\memberworkoutprogramcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|solutionrelative:webapi\\controllers\\memberworkoutprogramcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\webapi\\controllers\\expensescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|solutionrelative:webapi\\controllers\\expensescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\abstract\\iexpenseservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\abstract\\iexpenseservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 149, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "UnifiedCompanyManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\UnifiedCompanyManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\UnifiedCompanyManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\UnifiedCompanyManager.cs*", "RelativeToolTip": "Business\\Concrete\\UnifiedCompanyManager.cs*", "ViewState": "AgIAACoAAAAAAAAAAAAAAAUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T15:36:46.199Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "EfWorkoutProgramTemplateDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfWorkoutProgramTemplateDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfWorkoutProgramTemplateDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfWorkoutProgramTemplateDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfWorkoutProgramTemplateDal.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAnwCcAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-07T06:45:20.559Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "EfCompanyUserDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfCompanyUserDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfCompanyUserDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfCompanyUserDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfCompanyUserDal.cs", "ViewState": "AgIAAAEAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T10:23:24.662Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "EfWorkoutProgramDayDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfWorkoutProgramDayDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfWorkoutProgramDayDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfWorkoutProgramDayDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfWorkoutProgramDayDal.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T10:24:05.248Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "GymContext.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\GymContext.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\GymContext.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\GymContext.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\GymContext.cs", "ViewState": "AgIAABUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T10:24:00.271Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "EfUserDeviceDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfUserDeviceDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfUserDeviceDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfUserDeviceDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfUserDeviceDal.cs", "ViewState": "AgIAAAQAAAAAAAAAAAAQwBsAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-17T19:47:52.585Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "EfUserOperationClaimDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfUserOperationClaimDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfUserOperationClaimDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfUserOperationClaimDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfUserOperationClaimDal.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T10:24:05.889Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "EfWorkoutProgramExerciseDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfWorkoutProgramExerciseDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfWorkoutProgramExerciseDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfWorkoutProgramExerciseDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfWorkoutProgramExerciseDal.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T10:24:06.894Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "Entities.csproj", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Entities.csproj", "RelativeDocumentMoniker": "Entities\\Entities.csproj", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\Entities.csproj", "RelativeToolTip": "Entities\\Entities.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-06-24T10:23:59.689Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "EfUserCompanyDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfUserCompanyDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfUserCompanyDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfUserCompanyDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfUserCompanyDal.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T10:23:35.535Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "EfCompanyExerciseDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfCompanyExerciseDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfCompanyExerciseDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfCompanyExerciseDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfCompanyExerciseDal.cs", "ViewState": "AgIAAB4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T10:23:21.918Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "EfCompanyDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfCompanyDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfCompanyDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfCompanyDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfCompanyDal.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T10:23:20.156Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "CompanyController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Controllers\\CompanyController.cs", "RelativeDocumentMoniker": "WebAPI\\Controllers\\CompanyController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Controllers\\CompanyController.cs", "RelativeToolTip": "WebAPI\\Controllers\\CompanyController.cs", "ViewState": "AgIAACEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T10:21:49.625Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "DebtPaymentManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\DebtPaymentManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\DebtPaymentManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\DebtPaymentManager.cs", "RelativeToolTip": "Business\\Concrete\\DebtPaymentManager.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T10:12:41.824Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "CompanyUserManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\CompanyUserManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\CompanyUserManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\CompanyUserManager.cs", "RelativeToolTip": "Business\\Concrete\\CompanyUserManager.cs", "ViewState": "AgIAADwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T10:12:37.909Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "CompanyAdressManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\CompanyAdressManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\CompanyAdressManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\CompanyAdressManager.cs", "RelativeToolTip": "Business\\Concrete\\CompanyAdressManager.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T10:12:22.632Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "CompanyExerciseManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\CompanyExerciseManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\CompanyExerciseManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\CompanyExerciseManager.cs", "RelativeToolTip": "Business\\Concrete\\CompanyExerciseManager.cs", "ViewState": "AgIAAEIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T10:12:16.064Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "CompanyManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\CompanyManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\CompanyManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\CompanyManager.cs", "RelativeToolTip": "Business\\Concrete\\CompanyManager.cs", "ViewState": "AgIAABgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T10:12:13.829Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "MemberManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\MemberManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\MemberManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\MemberManager.cs", "RelativeToolTip": "Business\\Concrete\\MemberManager.cs", "ViewState": "AgIAAOUBAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-15T20:33:47.551Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Program.cs", "RelativeDocumentMoniker": "WebAPI\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Program.cs", "RelativeToolTip": "WebAPI\\Program.cs", "ViewState": "AgIAAF4AAAAAAAAAAADgv3wAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-15T20:04:16.269Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "SystemExerciseDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\DTOs\\SystemExerciseDto.cs", "RelativeDocumentMoniker": "Entities\\DTOs\\SystemExerciseDto.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Entities\\DTOs\\SystemExerciseDto.cs", "RelativeToolTip": "Entities\\DTOs\\SystemExerciseDto.cs", "ViewState": "AgIAACQAAAAAAAAAAAAtwDEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-15T19:48:08.634Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\appsettings.json", "RelativeDocumentMoniker": "WebAPI\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\appsettings.json", "RelativeToolTip": "WebAPI\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAAHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-13T20:22:12.778Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "EfMemberWorkoutProgramDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfMemberWorkoutProgramDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfMemberWorkoutProgramDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfMemberWorkoutProgramDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfMemberWorkoutProgramDal.cs", "ViewState": "AgIAACoBAAAAAAAAAAAAAEkBAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-09T18:22:26.998Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "MemberWorkoutProgramManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\MemberWorkoutProgramManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\MemberWorkoutProgramManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\MemberWorkoutProgramManager.cs", "RelativeToolTip": "Business\\Concrete\\MemberWorkoutProgramManager.cs", "ViewState": "AgIAAAcBAAAAAAAAAAAAwDkBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-09T18:22:35.845Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "MemberWorkoutProgramController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Controllers\\MemberWorkoutProgramController.cs", "RelativeDocumentMoniker": "WebAPI\\Controllers\\MemberWorkoutProgramController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Controllers\\MemberWorkoutProgramController.cs", "RelativeToolTip": "WebAPI\\Controllers\\MemberWorkoutProgramController.cs", "ViewState": "AgIAAIkAAAAAAAAAAAAnwJgAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-09T18:22:23.342Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "ExpensesController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Controllers\\ExpensesController.cs", "RelativeDocumentMoniker": "WebAPI\\Controllers\\ExpensesController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Controllers\\ExpensesController.cs", "RelativeToolTip": "WebAPI\\Controllers\\ExpensesController.cs", "ViewState": "AgIAAGAAAAAAAAAAAAAnwHgAAAApAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-07T01:34:49.706Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "IExpenseService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Abstract\\IExpenseService.cs", "RelativeDocumentMoniker": "Business\\Abstract\\IExpenseService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Abstract\\IExpenseService.cs", "RelativeToolTip": "Business\\Abstract\\IExpenseService.cs", "ViewState": "AgIAAAsAAAAAAAAAAAAnwBoAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-07T01:34:44.46Z"}]}]}]}