﻿using Core.Entities.Concrete;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Abstract
{
    public interface ICompanyUserService
    {
        
        IDataResult<List<CompanyUser>> GetAll();
        IResult Add(CompanyUser companyUser);
        IResult Update(CompanyUser companyUser);
        IResult Delete(int id);
        IDataResult<List<CompanyUser>> GetByCityId(int cityId);
        IDataResult<List<CompanyDetailDto>> GetCompanyDetails();
        IDataResult<List<CompanyDetailDto>> GetCompanyUserDetailsByCityId(int cityId);
        IDataResult<List<CompanyUserDetailDto>> GetCompanyUserDetails();

    }
}
