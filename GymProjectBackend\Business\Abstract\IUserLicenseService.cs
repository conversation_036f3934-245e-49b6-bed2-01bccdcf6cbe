﻿using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;

namespace Business.Abstract
{
    public interface IUserLicenseService
    {
        IDataResult<List<UserLicenseDto>> GetAll();
        IDataResult<UserLicenseDto> GetById(int id);
        IDataResult<List<UserLicenseDto>> GetActiveByUserId(int userId);
        IDataResult<List<UserLicenseDto>> GetMyActiveLicenses(int userId);
        IResult Add(UserLicense userLicense);
        IResult Update(UserLicense userLicense);
        IResult Delete(int id);
        IResult Purchase(LicensePurchaseDto licensePurchaseDto);
        IResult ExtendLicense(int userLicenseId, int extensionDays);
        IResult RevokeLicense(int userLicenseId);
        IDataResult<List<string>> GetUserRoles(int userId);
    }

}
