﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Validation;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class CompanyUserManager : ICompanyUserService
    {
        ICompanyUserDal _companyUserDal;

        public CompanyUserManager(ICompanyUserDal companyUserDal)
        {
            _companyUserDal = companyUserDal;
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("CompanyUser")]
        [ValidationAspect(typeof(CompanyUserValidator))]
        public IResult Add(CompanyUser companyUser)
        {
            _companyUserDal.Add(companyUser);
            return new SuccessResult(Messages.UserAdded);
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [SmartCacheRemoveAspect("CompanyUser")]
        public IResult Delete(int id)
        {
            _companyUserDal.Delete(id);
            return new SuccessResult(Messages.UserDeleted);
        }
        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 480, "CompanyUser", "Master")]
        public IDataResult<List<CompanyUser>> GetAll()
        {
            return new SuccessDataResult<List<CompanyUser>>(_companyUserDal.GetAll(), Messages.CompanyUserGetAll);
        }
        [SecuredOperation("owner")]
        [MultiTenantCacheAspect(duration: 480, "CompanyUser", "City")]
        public IDataResult<List<CompanyUser>> GetByCityId(int cityId)
        {
            return new SuccessDataResult<List<CompanyUser>>(_companyUserDal.GetAll(c => c.CityID == cityId));

        }
        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 480, "CompanyUser", "Details")]
        public IDataResult<List<CompanyUserDetailDto>> GetCompanyUserDetails()
        {
            return new SuccessDataResult<List<CompanyUserDetailDto>>(_companyUserDal.GetCompanyUserDetails());
        }
        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 480, "CompanyUser", "CompanyDetails")]
        public IDataResult<List<CompanyDetailDto>> GetCompanyDetails()
        {
            return new SuccessDataResult<List<CompanyDetailDto>>(_companyUserDal.GetCompanyDetails());
        }
        [SecuredOperation("owner")]
        [MultiTenantCacheAspect(duration: 480, "CompanyUser", "CityDetails")]
        public IDataResult<List<CompanyDetailDto>> GetCompanyUserDetailsByCityId(int cityId)
        {
            return new SuccessDataResult<List<CompanyDetailDto>>(_companyUserDal.GetCompanyUserDetailsByCityId(cityId));
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("CompanyUser")]
        [ValidationAspect(typeof(CompanyUserValidator))]
        public IResult Update(CompanyUser companyUser)
        {
            _companyUserDal.Update(companyUser);
            return new SuccessResult(Messages.UserUpdated);
        }
    }
}
