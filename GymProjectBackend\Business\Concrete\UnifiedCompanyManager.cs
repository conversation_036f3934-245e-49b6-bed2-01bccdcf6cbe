using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using Core.Utilities.Security.Hashing;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class UnifiedCompanyManager : IUnifiedCompanyService
    {
        private readonly IUserService _userService;
        private readonly ICompanyService _companyService;
        private readonly ICompanyAdressService _companyAdressService;
        private readonly ICompanyUserService _companyUserService;
        private readonly IUserCompanyService _userCompanyService;

        public UnifiedCompanyManager(
            IUserService userService,
            ICompanyService companyService,
            ICompanyAdressService companyAdressService,
            ICompanyUserService companyUserService,
            IUserCompanyService userCompanyService)
        {
            _userService = userService;
            _companyService = companyService;
            _companyAdressService = companyAdressService;
            _companyUserService = companyUserService;
            _userCompanyService = userCompanyService;
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(5)]
        public IResult AddUnifiedCompany(UnifiedCompanyAddDto unifiedCompanyDto)
        {
            try
            {
                // 1. User oluştur (Adım 1'den gelen bilgiler)
                byte[] passwordHash, passwordSalt;
                HashingHelper.CreatePasswordHash(unifiedCompanyDto.UserPassword, out passwordHash, out passwordSalt);

                var user = new User
                {
                    FirstName = unifiedCompanyDto.UserFirstName,
                    LastName = unifiedCompanyDto.UserLastName,
                    Email = unifiedCompanyDto.UserEmail,
                    PasswordHash = passwordHash,
                    PasswordSalt = passwordSalt,
                    IsActive = true,
                    RequirePasswordChange = false, // İlk kayıtta false, geçici şifre atandığında true olacak
                    CreationDate = DateTime.Now
                };

                var userResult = _userService.Add(user);
                if (!userResult.Success)
                {
                    return new ErrorResult("Kullanıcı oluşturulamadı: " + userResult.Message);
                }

                // User ID'sini almak için tekrar sorgula
                var createdUser = _userService.GetByMail(unifiedCompanyDto.UserEmail);
                if (createdUser == null)
                {
                    return new ErrorResult("Oluşturulan kullanıcı bulunamadı");
                }

                // 2. Company oluştur (Adım 2'den gelen bilgiler)
                var company = new Company
                {
                    CompanyName = unifiedCompanyDto.CompanyName,
                    PhoneNumber = unifiedCompanyDto.CompanyPhone,
                    IsActive = true,
                    CreationDate = DateTime.Now
                };

                var companyResult = _companyService.Add(company);
                if (!companyResult.Success)
                {
                    return new ErrorResult("Şirket oluşturulamadı: " + companyResult.Message);
                }

                // Company ID'sini almak için tekrar sorgula
                var companies = _companyService.GetAll();
                var createdCompany = companies.Data?.FirstOrDefault(c => c.CompanyName == unifiedCompanyDto.CompanyName);
                if (createdCompany == null)
                {
                    return new ErrorResult("Oluşturulan şirket bulunamadı");
                }

                // 3. CompanyAdress oluştur (Adım 3'ten gelen bilgiler)
                var companyAddress = new CompanyAdress
                {
                    CompanyID = createdCompany.CompanyID,
                    CityID = unifiedCompanyDto.CityID,
                    TownID = unifiedCompanyDto.TownID,
                    Adress = unifiedCompanyDto.Address,
                    IsActive = true,
                    CreationDate = DateTime.Now
                };

                var addressResult = _companyAdressService.Add(companyAddress);
                if (!addressResult.Success)
                {
                    return new ErrorResult("Şirket adresi oluşturulamadı: " + addressResult.Message);
                }

                // 4. CompanyUser oluştur (Adım 4'ten gelen bilgiler)
                var companyUser = new CompanyUser
                {
                    CityID = unifiedCompanyDto.CityID,
                    TownID = unifiedCompanyDto.TownID,
                    Name = unifiedCompanyDto.OwnerFullName,
                    PhoneNumber = unifiedCompanyDto.OwnerPhone,
                    Email = unifiedCompanyDto.UserEmail, // Adım 1'den gelen email
                    IsActive = true,
                    CreationDate = DateTime.Now
                };

                var companyUserResult = _companyUserService.Add(companyUser);
                if (!companyUserResult.Success)
                {
                    return new ErrorResult("Şirket sahibi oluşturulamadı: " + companyUserResult.Message);
                }

                // CompanyUser ID'sini almak için tekrar sorgula
                var companyUsers = _companyUserService.GetAll();
                var createdCompanyUser = companyUsers.Data?.FirstOrDefault(cu => 
                    cu.Email == unifiedCompanyDto.UserEmail && cu.PhoneNumber == unifiedCompanyDto.OwnerPhone);
                if (createdCompanyUser == null)
                {
                    return new ErrorResult("Oluşturulan şirket sahibi bulunamadı");
                }

                // 5. UserCompany ilişkisi oluştur
                var userCompany = new UserCompany
                {
                    UserID = createdCompanyUser.CompanyUserID, // CompanyUser ID'si UserCompany'de UserID olarak kullanılıyor
                    CompanyId = createdCompany.CompanyID,
                    IsActive = true,
                    CreationDate = DateTime.Now
                };

                var userCompanyResult = _userCompanyService.Add(userCompany);
                if (!userCompanyResult.Success)
                {
                    return new ErrorResult("Kullanıcı-şirket ilişkisi oluşturulamadı: " + userCompanyResult.Message);
                }

                // 6. Geçici şifre ataması (telefon numarasının son 4 hanesi)
                if (!string.IsNullOrEmpty(unifiedCompanyDto.OwnerPhone) && unifiedCompanyDto.OwnerPhone.Length >= 4)
                {
                    string tempPassword = unifiedCompanyDto.OwnerPhone.Substring(unifiedCompanyDto.OwnerPhone.Length - 4);
                    
                    // User'ın şifresini geçici şifre ile güncelle
                    byte[] tempPasswordHash, tempPasswordSalt;
                    HashingHelper.CreatePasswordHash(tempPassword, out tempPasswordHash, out tempPasswordSalt);
                    
                    createdUser.PasswordHash = tempPasswordHash;
                    createdUser.PasswordSalt = tempPasswordSalt;
                    createdUser.RequirePasswordChange = true;
                    createdUser.UpdatedDate = DateTime.Now;

                    var updateResult = _userService.Update(createdUser);
                    if (!updateResult.Success)
                    {
                        return new ErrorResult("Geçici şifre atanamadı: " + updateResult.Message);
                    }

                    return new SuccessResult($"Salon başarıyla oluşturuldu. Geçici şifre: {tempPassword}");
                }

                return new SuccessResult("Salon başarıyla oluşturuldu.");
            }
            catch (Exception ex)
            {
                return new ErrorResult("Salon oluşturulurken bir hata oluştu: " + ex.Message);
            }
        }
    }
}
