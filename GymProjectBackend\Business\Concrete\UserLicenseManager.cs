﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Transactions;

namespace Business.Concrete
{
    public class UserLicenseManager : IUserLicenseService
    {
        private readonly IUserLicenseDal _userLicenseDal;
        private readonly ILicensePackageDal _licensePackageDal;
        private readonly ILicenseTransactionService _licenseTransactionService;
        private readonly IUserService _userService;

        public UserLicenseManager(
            IUserLicenseDal userLicenseDal,
            ILicensePackageDal licensePackageDal,
            ILicenseTransactionService licenseTransactionService,
            IUserService userService)
        {
            _userLicenseDal = userLicenseDal;
            _licensePackageDal = licensePackageDal;
            _licenseTransactionService = licenseTransactionService;
            _userService = userService;
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("UserLicense")]
        public IResult Add(UserLicense userLicense)
        {
            userLicense.CreationDate = DateTime.Now;
            userLicense.IsActive = true;
            _userLicenseDal.Add(userLicense);
            return new SuccessResult("Kullanıcı lisansı başarıyla eklendi");
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [SmartCacheRemoveAspect("UserLicense")]
        public IResult Delete(int id)
        {
            _userLicenseDal.Delete(id);
            return new SuccessResult("Kullanıcı lisansı başarıyla silindi");
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 60, "UserLicense", "Business")]
        public IDataResult<List<UserLicenseDto>> GetAll()
        {
            return new SuccessDataResult<List<UserLicenseDto>>(_userLicenseDal.GetUserLicenseDetails());
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 60, "UserLicense", "Active")]
        public IDataResult<List<UserLicenseDto>> GetActiveByUserId(int userId)
        {
            return new SuccessDataResult<List<UserLicenseDto>>(_userLicenseDal.GetActiveUserLicensesByUserId(userId));
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 60, "UserLicense", "MyActive")]
        public IDataResult<List<UserLicenseDto>> GetMyActiveLicenses(int userId)
        {
            return new SuccessDataResult<List<UserLicenseDto>>(_userLicenseDal.GetActiveUserLicensesByUserId(userId));
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 60, "UserLicense", "Details")]
        public IDataResult<UserLicenseDto> GetById(int id)
        {
            return new SuccessDataResult<UserLicenseDto>(_userLicenseDal.GetUserLicenseDetail(id));
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("UserLicense")]
        public IResult Update(UserLicense userLicense)
        {
            userLicense.UpdatedDate = DateTime.Now;
            _userLicenseDal.Update(userLicense);
            return new SuccessResult("Kullanıcı lisansı başarıyla güncellendi");
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("UserLicense")]
        public IResult Purchase(LicensePurchaseDto licensePurchaseDto)
        {
            using (var scope = new TransactionScope())
            {
                try
                {
                    var user = _userService.GetById(licensePurchaseDto.UserID);
                    if (!user.Success)
                    {
                        return new ErrorResult("Kullanıcı bulunamadı");
                    }

                    var licensePackage = _licensePackageDal.Get(lp => lp.LicensePackageID == licensePurchaseDto.LicensePackageID);
                    if (licensePackage == null)
                    {
                        return new ErrorResult("Lisans paketi bulunamadı");
                    }

                    var now = DateTime.Now;
                    var endDate = now.AddDays(licensePackage.DurationDays);

                    // Aynı role ait aktif lisans var mı diye kontrol et
                    var existingLicenses = _userLicenseDal.GetAll(ul =>
                        ul.UserID == licensePurchaseDto.UserID &&
                        ul.IsActive &&
                        ul.EndDate > now);

                    foreach (var existingLicense in existingLicenses)
                    {
                        var existingPackage = _licensePackageDal.Get(lp => lp.LicensePackageID == existingLicense.LicensePackageID);
                        if (existingPackage.Role == licensePackage.Role)
                        {
                            // Aynı rol için lisans zaten var, mevcut lisansı uzatalım
                            return ExtendLicense(existingLicense.UserLicenseID, licensePackage.DurationDays);
                        }
                    }

                    // Yeni lisans oluştur
                    var userLicense = new UserLicense
                    {
                        UserID = licensePurchaseDto.UserID,
                        LicensePackageID = licensePurchaseDto.LicensePackageID,
                        StartDate = now,
                        EndDate = endDate,
                        IsActive = true,
                        CreationDate = now
                    };
                    _userLicenseDal.Add(userLicense);

                    // Ödeme işlemini kaydet
                    var transaction = new LicenseTransaction
                    {
                        UserID = licensePurchaseDto.UserID,
                        LicensePackageID = licensePurchaseDto.LicensePackageID,
                        UserLicenseID = userLicense.UserLicenseID,
                        Amount = licensePackage.Price,
                        PaymentMethod = licensePurchaseDto.PaymentMethod,
                        TransactionDate = now,
                        IsActive = true,
                        CreationDate = now
                    };
                    _licenseTransactionService.Add(transaction);

                    scope.Complete();
                    return new SuccessResult("Lisans başarıyla satın alındı");
                }
                catch (Exception ex)
                {
                    return new ErrorResult($"Lisans satın alınırken bir hata oluştu: {ex.Message}");
                }
            }
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("UserLicense")]
        public IResult ExtendLicense(int userLicenseId, int extensionDays)
        {
            var userLicense = _userLicenseDal.Get(ul => ul.UserLicenseID == userLicenseId);
            if (userLicense == null)
            {
                return new ErrorResult("Lisans bulunamadı");
            }

            var licensePackage = _licensePackageDal.Get(lp => lp.LicensePackageID == userLicense.LicensePackageID);
            if (licensePackage == null)
            {
                return new ErrorResult("Lisans paketi bulunamadı");
            }

            using (var scope = new TransactionScope())
            {
                try
                {
                    // Lisansı uzat
                    var now = DateTime.Now;
                    userLicense.EndDate = userLicense.EndDate > now
                        ? userLicense.EndDate.AddDays(extensionDays)
                        : now.AddDays(extensionDays);
                    userLicense.IsActive = true;
                    userLicense.UpdatedDate = now;
                    _userLicenseDal.Update(userLicense);

                    // Ödeme işlemini kaydet
                    var transaction = new LicenseTransaction
                    {
                        UserID = userLicense.UserID,
                        LicensePackageID = userLicense.LicensePackageID,
                        UserLicenseID = userLicense.UserLicenseID,
                        Amount = licensePackage.Price * extensionDays / licensePackage.DurationDays,
                        PaymentMethod = "Uzatma",
                        TransactionDate = now,
                        IsActive = true,
                        CreationDate = now
                    };
                    _licenseTransactionService.Add(transaction);

                    scope.Complete();
                    return new SuccessResult("Lisans başarıyla uzatıldı");
                }
                catch (Exception ex)
                {
                    return new ErrorResult($"Lisans uzatılırken bir hata oluştu: {ex.Message}");
                }
            }
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("UserLicense")]
        public IResult RevokeLicense(int userLicenseId)
        {
            var userLicense = _userLicenseDal.Get(ul => ul.UserLicenseID == userLicenseId);
            if (userLicense == null)
            {
                return new ErrorResult("Lisans bulunamadı");
            }

            userLicense.IsActive = false;
            userLicense.EndDate = DateTime.Now;
            userLicense.UpdatedDate = DateTime.Now;
            _userLicenseDal.Update(userLicense);

            return new SuccessResult("Lisans başarıyla iptal edildi");
        }

        public IDataResult<List<string>> GetUserRoles(int userId)
        {
            var now = DateTime.Now;
            var activeLicenses = _userLicenseDal.GetAll(ul =>
                ul.UserID == userId &&
                ul.IsActive &&
                ul.EndDate > now);

            var roles = new List<string>();

            foreach (var license in activeLicenses)
            {
                var package = _licensePackageDal.Get(lp => lp.LicensePackageID == license.LicensePackageID);
                if (package != null && !roles.Contains(package.Role))
                {
                    roles.Add(package.Role);
                }
            }

            return new SuccessDataResult<List<string>>(roles);
        }
    }
}