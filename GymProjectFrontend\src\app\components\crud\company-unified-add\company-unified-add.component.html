<div class="container-fluid mt-4">
  <div *ngIf="isSubmitting" class="d-flex justify-content-center align-items-center" style="height: 100vh;">
    <app-loading-spinner></app-loading-spinner>
  </div>

  <div class="fade-in" [class.content-blur]="isSubmitting">
    <!-- Wizard Header -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="modern-card slide-in-down">
          <div class="modern-card-header">
            <h4><i class="fas fa-plus-circle me-2"></i>Yeni Salon Ekleme Sihirbazı</h4>
          </div>
          <div class="modern-card-body">
            <!-- Progress Steps -->
            <div class="wizard-progress">
              <div class="wizard-steps">
                <div *ngFor="let step of [WizardStep.USER_REGISTRATION, WizardStep.COMPANY_INFO, WizardStep.ADDRESS_INFO, WizardStep.OWNER_INFO, WizardStep.PREVIEW]; let i = index"
                     class="wizard-step"
                     [class.active]="currentStep === step"
                     [class.completed]="isStepCompleted(step)"
                     [class.accessible]="isStepAccessible(step)"
                     (click)="isStepAccessible(step) ? goToStep(step) : null">
                  <div class="wizard-step-icon">
                    <fa-icon [icon]="getStepIcon(step)"></fa-icon>
                  </div>
                  <div class="wizard-step-title">{{ getStepTitle(step) }}</div>
                  <div class="wizard-step-line" *ngIf="i < 4"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Wizard Content -->
    <div class="row">
      <div class="col-12">
        <div class="modern-card slide-in-up">
          <div class="modern-card-body">

            <!-- Step 1: User Registration -->
            <div *ngIf="currentStep === WizardStep.USER_REGISTRATION" class="wizard-step-content fade-in">
              <div class="step-header">
                <h5><fa-icon [icon]="faUser" class="me-2"></fa-icon>Kullanıcı Kaydı</h5>
                <p class="text-muted">Salon sahibi kullanıcı bilgilerini giriniz</p>
              </div>

              <form [formGroup]="userRegistrationForm" class="wizard-form">
                <div class="row">
                  <div class="col-md-6">
                    <mat-form-field appearance="outline" class="modern-mat-form-field">
                      <mat-label>Ad</mat-label>
                      <input matInput formControlName="firstName" placeholder="Adınızı giriniz">
                      <mat-icon matPrefix>person</mat-icon>
                      <mat-error *ngIf="userRegistrationForm.get('firstName')?.hasError('required')">
                        Ad zorunludur
                      </mat-error>
                    </mat-form-field>
                  </div>
                  <div class="col-md-6">
                    <mat-form-field appearance="outline" class="modern-mat-form-field">
                      <mat-label>Soyad</mat-label>
                      <input matInput formControlName="lastName" placeholder="Soyadınızı giriniz">
                      <mat-icon matPrefix>person</mat-icon>
                      <mat-error *ngIf="userRegistrationForm.get('lastName')?.hasError('required')">
                        Soyad zorunludur
                      </mat-error>
                    </mat-form-field>
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-6">
                    <mat-form-field appearance="outline" class="modern-mat-form-field">
                      <mat-label>E-posta</mat-label>
                      <input matInput type="email" formControlName="email" placeholder="E-posta adresinizi giriniz">
                      <mat-icon matPrefix>email</mat-icon>
                      <mat-error *ngIf="userRegistrationForm.get('email')?.hasError('required')">
                        E-posta zorunludur
                      </mat-error>
                      <mat-error *ngIf="userRegistrationForm.get('email')?.hasError('email')">
                        Geçerli bir e-posta adresi giriniz
                      </mat-error>
                    </mat-form-field>
                  </div>
                  <div class="col-md-6">
                    <mat-form-field appearance="outline" class="modern-mat-form-field">
                      <mat-label>Şifre</mat-label>
                      <input matInput type="password" formControlName="password" placeholder="Şifrenizi giriniz">
                      <mat-icon matPrefix>lock</mat-icon>
                      <mat-error *ngIf="userRegistrationForm.get('password')?.hasError('required')">
                        Şifre zorunludur
                      </mat-error>
                      <mat-error *ngIf="userRegistrationForm.get('password')?.hasError('minlength')">
                        Şifre en az 6 karakter olmalıdır
                      </mat-error>
                    </mat-form-field>
                  </div>
                </div>
              </form>
            </div>

            <!-- Step 2: Company Info -->
            <div *ngIf="currentStep === WizardStep.COMPANY_INFO" class="wizard-step-content fade-in">
              <div class="step-header">
                <h5><fa-icon [icon]="faBuilding" class="me-2"></fa-icon>Şirket Bilgileri</h5>
                <p class="text-muted">Salon şirket bilgilerini giriniz</p>
              </div>

              <form [formGroup]="companyInfoForm" class="wizard-form">
                <div class="row">
                  <div class="col-md-6">
                    <mat-form-field appearance="outline" class="modern-mat-form-field">
                      <mat-label>Şirket Adı</mat-label>
                      <input matInput formControlName="companyName" placeholder="Şirket adını giriniz">
                      <mat-icon matPrefix>business</mat-icon>
                      <mat-error *ngIf="companyInfoForm.get('companyName')?.hasError('required')">
                        Şirket adı zorunludur
                      </mat-error>
                    </mat-form-field>
                  </div>
                  <div class="col-md-6">
                    <mat-form-field appearance="outline" class="modern-mat-form-field">
                      <mat-label>Telefon Numarası</mat-label>
                      <input matInput formControlName="companyPhone" placeholder="0XXX XXX XX XX" maxlength="11">
                      <mat-icon matPrefix>phone</mat-icon>
                      <mat-error *ngIf="companyInfoForm.get('companyPhone')?.hasError('required')">
                        Telefon numarası zorunludur
                      </mat-error>
                      <mat-error *ngIf="companyInfoForm.get('companyPhone')?.hasError('pattern')">
                        Geçerli bir telefon numarası giriniz (0XXX XXX XX XX)
                      </mat-error>
                    </mat-form-field>
                  </div>
                </div>
              </form>
            </div>

            <!-- Step 3: Address Info -->
            <div *ngIf="currentStep === WizardStep.ADDRESS_INFO" class="wizard-step-content fade-in">
              <div class="step-header">
                <h5><fa-icon [icon]="faMapMarkerAlt" class="me-2"></fa-icon>Adres Bilgileri</h5>
                <p class="text-muted">Salon adres bilgilerini giriniz</p>
              </div>

              <form [formGroup]="addressInfoForm" class="wizard-form">
                <div class="row">
                  <div class="col-md-6">
                    <mat-form-field appearance="outline" class="modern-mat-form-field">
                      <mat-label>İl</mat-label>
                      <input type="text" matInput formControlName="city" [matAutocomplete]="autoCity">
                      <mat-icon matPrefix>location_city</mat-icon>
                      <mat-autocomplete #autoCity="matAutocomplete" [displayWith]="displayCity">
                        <mat-option *ngFor="let city of filteredCities | async" [value]="city">
                          {{city.cityName}}
                        </mat-option>
                      </mat-autocomplete>
                      <mat-error *ngIf="addressInfoForm.get('city')?.hasError('required')">
                        İl seçimi zorunludur
                      </mat-error>
                    </mat-form-field>
                  </div>
                  <div class="col-md-6">
                    <mat-form-field appearance="outline" class="modern-mat-form-field">
                      <mat-label>İlçe</mat-label>
                      <input type="text" matInput formControlName="town" [matAutocomplete]="autoTown">
                      <mat-icon matPrefix>place</mat-icon>
                      <mat-autocomplete #autoTown="matAutocomplete" [displayWith]="displayTown">
                        <mat-option *ngFor="let town of filteredTowns | async" [value]="town">
                          {{town.townName}}
                        </mat-option>
                      </mat-autocomplete>
                      <mat-error *ngIf="addressInfoForm.get('town')?.hasError('required')">
                        İlçe seçimi zorunludur
                      </mat-error>
                    </mat-form-field>
                  </div>
                </div>

                <div class="row">
                  <div class="col-12">
                    <mat-form-field appearance="outline" class="modern-mat-form-field">
                      <mat-label>Adres</mat-label>
                      <textarea matInput formControlName="address" placeholder="Detaylı adresi giriniz" rows="3"></textarea>
                      <mat-icon matPrefix>home</mat-icon>
                      <mat-error *ngIf="addressInfoForm.get('address')?.hasError('required')">
                        Adres zorunludur
                      </mat-error>
                    </mat-form-field>
                  </div>
                </div>
              </form>
            </div>

            <!-- Step 4: Owner Info -->
            <div *ngIf="currentStep === WizardStep.OWNER_INFO" class="wizard-step-content fade-in">
              <div class="step-header">
                <h5><fa-icon [icon]="faPhone" class="me-2"></fa-icon>Salon Sahibi Bilgileri</h5>
                <p class="text-muted">Salon sahibi iletişim bilgilerini giriniz</p>
              </div>

              <form [formGroup]="ownerInfoForm" class="wizard-form">
                <div class="row">
                  <div class="col-md-6">
                    <mat-form-field appearance="outline" class="modern-mat-form-field">
                      <mat-label>Ad Soyad</mat-label>
                      <input matInput formControlName="ownerFullName" placeholder="Salon sahibi ad soyad">
                      <mat-icon matPrefix>person</mat-icon>
                      <mat-error *ngIf="ownerInfoForm.get('ownerFullName')?.hasError('required')">
                        Ad soyad zorunludur
                      </mat-error>
                    </mat-form-field>
                  </div>
                  <div class="col-md-6">
                    <mat-form-field appearance="outline" class="modern-mat-form-field">
                      <mat-label>Telefon Numarası</mat-label>
                      <input matInput formControlName="ownerPhone" placeholder="0XXX XXX XX XX" maxlength="11">
                      <mat-icon matPrefix>phone</mat-icon>
                      <mat-error *ngIf="ownerInfoForm.get('ownerPhone')?.hasError('required')">
                        Telefon numarası zorunludur
                      </mat-error>
                      <mat-error *ngIf="ownerInfoForm.get('ownerPhone')?.hasError('pattern')">
                        Geçerli bir telefon numarası giriniz (0XXX XXX XX XX)
                      </mat-error>
                    </mat-form-field>
                  </div>
                </div>
              </form>
            </div>

            <!-- Step 5: Preview -->
            <div *ngIf="currentStep === WizardStep.PREVIEW" class="wizard-step-content">
              <div class="step-header">
                <h5><fa-icon [icon]="faCheck" class="me-2"></fa-icon>Önizleme & Kaydet</h5>
                <p class="text-muted">Girilen bilgileri kontrol edin ve kaydedin</p>
              </div>

              <div class="preview-content">
                <div class="row">
                  <div class="col-md-6">
                    <div class="preview-section">
                      <h6><fa-icon [icon]="faUser" class="me-2"></fa-icon>Kullanıcı Bilgileri</h6>
                      <div class="preview-item">
                        <strong>Ad Soyad:</strong> {{ wizardData.userRegistration?.firstName }} {{ wizardData.userRegistration?.lastName }}
                      </div>
                      <div class="preview-item">
                        <strong>E-posta:</strong> {{ wizardData.userRegistration?.email }}
                      </div>
                    </div>

                    <div class="preview-section">
                      <h6><fa-icon [icon]="faBuilding" class="me-2"></fa-icon>Şirket Bilgileri</h6>
                      <div class="preview-item">
                        <strong>Şirket Adı:</strong> {{ wizardData.companyInfo?.companyName }}
                      </div>
                      <div class="preview-item">
                        <strong>Telefon:</strong> {{ wizardData.companyInfo?.companyPhone }}
                      </div>
                    </div>
                  </div>

                  <div class="col-md-6">
                    <div class="preview-section">
                      <h6><fa-icon [icon]="faMapMarkerAlt" class="me-2"></fa-icon>Adres Bilgileri</h6>
                      <div class="preview-item">
                        <strong>İl/İlçe:</strong> {{ wizardData.addressInfo?.city?.cityName }}/{{ wizardData.addressInfo?.town?.townName }}
                      </div>
                      <div class="preview-item">
                        <strong>Adres:</strong> {{ wizardData.addressInfo?.address }}
                      </div>
                    </div>

                    <div class="preview-section">
                      <h6><fa-icon [icon]="faPhone" class="me-2"></fa-icon>Salon Sahibi</h6>
                      <div class="preview-item">
                        <strong>Ad Soyad:</strong> {{ wizardData.ownerInfo?.ownerFullName }}
                      </div>
                      <div class="preview-item">
                        <strong>Telefon:</strong> {{ wizardData.ownerInfo?.ownerPhone }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Wizard Navigation -->
            <div class="wizard-navigation">
              <div class="d-flex justify-content-between">
                <button
                  type="button"
                  class="modern-btn modern-btn-secondary"
                  [disabled]="currentStep === WizardStep.USER_REGISTRATION"
                  (click)="previousStep()">
                  <fa-icon [icon]="faArrowLeft" class="me-2"></fa-icon>Önceki
                </button>

                <button
                  *ngIf="currentStep !== WizardStep.PREVIEW"
                  type="button"
                  class="modern-btn modern-btn-primary"
                  [disabled]="!isCurrentStepValid()"
                  (click)="nextStep()">
                  Sonraki<fa-icon [icon]="faArrowRight" class="ms-2"></fa-icon>
                </button>

                <button
                  *ngIf="currentStep === WizardStep.PREVIEW"
                  type="button"
                  class="modern-btn modern-btn-success"
                  [disabled]="isSubmitting"
                  (click)="submitUnifiedCompany()">
                  <fa-icon [icon]="faSave" class="me-2"></fa-icon>
                  {{ isSubmitting ? 'Kaydediliyor...' : 'Salonu Kaydet' }}
                </button>
              </div>
            </div>

          </div>
        </div>
      </div>

    </div>
  </div>
</div>

<style>
  .content-blur {
    filter: blur(3px);
    pointer-events: none;
  }

  /* Wizard Progress Styles */
  .wizard-progress {
    padding: 2rem 0;
  }

  .wizard-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    max-width: 800px;
    margin: 0 auto;
  }

  .wizard-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 2;
  }

  .wizard-step.accessible:hover {
    transform: translateY(-2px);
  }

  .wizard-step-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--bg-secondary);
    border: 3px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    font-size: 1.2rem;
    transition: all 0.3s ease;
    margin-bottom: 0.5rem;
  }

  .wizard-step.active .wizard-step-icon {
    background-color: var(--primary);
    border-color: var(--primary);
    color: white;
    box-shadow: 0 0 0 4px rgba(var(--primary-rgb), 0.2);
  }

  .wizard-step.completed .wizard-step-icon {
    background-color: var(--success);
    border-color: var(--success);
    color: white;
  }

  .wizard-step-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    text-align: center;
    max-width: 100px;
    line-height: 1.2;
  }

  .wizard-step.active .wizard-step-title {
    color: var(--primary);
    font-weight: 600;
  }

  .wizard-step.completed .wizard-step-title {
    color: var(--success);
  }

  .wizard-step-line {
    position: absolute;
    top: 25px;
    left: 50px;
    width: calc(100% + 50px);
    height: 3px;
    background-color: var(--border-color);
    z-index: 1;
  }

  .wizard-step.completed .wizard-step-line {
    background-color: var(--success);
  }

  /* Wizard Content Styles */
  .wizard-step-content {
    min-height: 400px;
    padding: 2rem;
  }

  .step-header {
    text-align: center;
    margin-bottom: 2rem;
  }

  .step-header h5 {
    color: var(--primary);
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  .wizard-form {
    max-width: 600px;
    margin: 0 auto;
  }

  .wizard-navigation {
    padding: 1.5rem 2rem;
    border-top: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
  }

  /* Preview Styles */
  .preview-content {
    max-width: 800px;
    margin: 0 auto;
  }

  .preview-section {
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius-md);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid var(--border-color);
  }

  .preview-section h6 {
    color: var(--primary);
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
  }

  .preview-item {
    margin-bottom: 0.75rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
  }

  .preview-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }

  .preview-item strong {
    color: var(--text-primary);
    margin-right: 0.5rem;
  }

  /* Material Form Field Overrides */
  .modern-mat-form-field {
    width: 100%;
  }

  ::ng-deep .modern-mat-form-field.mat-form-field-appearance-outline .mat-form-field-outline {
    color: var(--border-color);
  }

  ::ng-deep .modern-mat-form-field.mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
    color: var(--primary);
  }

  ::ng-deep .modern-mat-form-field .mat-form-field-label {
    color: var(--text-secondary);
  }

  ::ng-deep .modern-mat-form-field.mat-focused .mat-form-field-label {
    color: var(--primary);
  }

  ::ng-deep .modern-mat-form-field .mat-input-element {
    color: var(--text-primary);
  }

  ::ng-deep .modern-mat-form-field .mat-form-field-prefix {
    color: var(--primary);
    margin-right: 8px;
  }

  /* Animation Classes */
  .slide-in-left {
    animation: slideInLeft 0.5s ease-out;
  }

  .slide-in-right {
    animation: slideInRight 0.5s ease-out;
  }

  .fade-in {
    animation: fadeIn 0.3s ease-in;
  }

  @keyframes slideInLeft {
    from {
      transform: translateX(-100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .wizard-steps {
      flex-wrap: wrap;
      gap: 0.5rem;
      justify-content: center;
    }

    .wizard-step {
      flex: 0 0 auto;
      margin: 0 0.25rem;
    }

    .wizard-step-icon {
      width: 40px;
      height: 40px;
      font-size: 1rem;
    }

    .wizard-step-title {
      font-size: 0.75rem;
      max-width: 80px;
    }

    .wizard-step-line {
      display: none;
    }

    .wizard-step-content {
      padding: 1rem;
      min-height: auto;
    }

    .wizard-navigation {
      padding: 1rem;
    }

    .preview-content .row {
      margin: 0;
    }

    .preview-content .col-md-6 {
      padding: 0.5rem;
    }
  }

  @media (max-width: 576px) {
    .wizard-steps {
      flex-direction: column;
      align-items: center;
    }

    .wizard-step {
      margin-bottom: 1rem;
    }

    .wizard-step-content {
      padding: 0.5rem;
    }

    .wizard-navigation .d-flex {
      flex-direction: column;
      gap: 1rem;
    }

    .wizard-navigation button {
      width: 100%;
    }
  }
</style>
