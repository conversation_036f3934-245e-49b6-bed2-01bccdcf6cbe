import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { CityService } from '../../../services/city.service';
import { TownService } from '../../../services/town.service';
import { UnifiedCompanyService, UnifiedCompanyAddDto } from '../../../services/unified-company.service';
import { City } from '../../../models/city';
import { Town } from '../../../models/town';
import { Observable } from 'rxjs';
import { startWith, map } from 'rxjs/operators';
import {
  faPlus, faMinus, faEdit, faSave, faArrowLeft, faCalendarAlt,
  faArrowRight, faCheck, faInfoCircle, faClock, faDumbbell, faEye,
  faUser, faBuilding, faMapMarkerAlt, faPhone
} from '@fortawesome/free-solid-svg-icons';

// Wizard adımları enum'u
enum WizardStep {
  USER_REGISTRATION = 1,
  COMPANY_INFO = 2,
  ADDRESS_INFO = 3,
  OWNER_INFO = 4,
  PREVIEW = 5
}

@Component({
  selector: 'app-company-unified-add',
  templateUrl: './company-unified-add.component.html',
  styleUrls: ['./company-unified-add.component.css'],
  standalone: false
})
export class CompanyUnifiedAddComponent implements OnInit {
  // Icons
  faPlus = faPlus;
  faMinus = faMinus;
  faEdit = faEdit;
  faSave = faSave;
  faArrowLeft = faArrowLeft;
  faCalendarAlt = faCalendarAlt;
  faArrowRight = faArrowRight;
  faCheck = faCheck;
  faInfoCircle = faInfoCircle;
  faClock = faClock;
  faDumbbell = faDumbbell;
  faEye = faEye;
  faUser = faUser;
  faBuilding = faBuilding;
  faMapMarkerAlt = faMapMarkerAlt;
  faPhone = faPhone;

  // Wizard state
  currentStep: WizardStep = WizardStep.USER_REGISTRATION;
  WizardStep = WizardStep; // Template'de kullanmak için

  // Forms
  userRegistrationForm!: FormGroup;
  companyInfoForm!: FormGroup;
  addressInfoForm!: FormGroup;
  ownerInfoForm!: FormGroup;
  isSubmitting = false;

  // Data
  cities: City[] = [];
  towns: Town[] = [];
  filteredCities: Observable<City[]>;
  filteredTowns: Observable<Town[]>;

  // Wizard data storage
  wizardData: any = {};

  // Basit ilerleme sistemi
  completedSteps: Set<WizardStep> = new Set();

  constructor(
    private formBuilder: FormBuilder,
    private cityService: CityService,
    private townService: TownService,
    private unifiedCompanyService: UnifiedCompanyService,
    private toastrService: ToastrService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.initializeForms();
    this.getCities();
    this.setupCityAutocomplete();
  }

  initializeForms() {
    // Adım 1: Kullanıcı Kaydı
    this.userRegistrationForm = this.formBuilder.group({
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });

    // Adım 2: Şirket Bilgileri
    this.companyInfoForm = this.formBuilder.group({
      companyName: ['', [Validators.required, Validators.minLength(2)]],
      companyPhone: ['', [Validators.required, Validators.pattern(/^0\d{10}$/)]]
    });

    // Adım 3: Adres Bilgileri
    this.addressInfoForm = this.formBuilder.group({
      city: ['', Validators.required],
      town: ['', Validators.required],
      address: ['', [Validators.required, Validators.minLength(10)]]
    });

    // Adım 4: Salon Sahibi Bilgileri
    this.ownerInfoForm = this.formBuilder.group({
      ownerFullName: ['', [Validators.required, Validators.minLength(5)]],
      ownerPhone: ['', [Validators.required, Validators.pattern(/^0\d{10}$/)]]
    });
  }

  // Wizard Navigation Methods
  nextStep(): void {
    if (this.isCurrentStepValid()) {
      this.saveCurrentStepData();
      this.completedSteps.add(this.currentStep);

      if (this.currentStep < WizardStep.PREVIEW) {
        this.currentStep++;
        this.setupStepSpecificLogic();
      }
    } else {
      this.toastrService.error('Lütfen tüm gerekli alanları doldurun', 'Hata');
    }
  }

  previousStep(): void {
    if (this.currentStep > WizardStep.USER_REGISTRATION) {
      this.currentStep--;
      this.setupStepSpecificLogic();
    }
  }

  goToStep(step: WizardStep): void {
    // Sadece tamamlanmış adımlara veya bir sonraki adıma gidebilir
    if (this.completedSteps.has(step) || step === this.getNextAvailableStep()) {
      this.currentStep = step;
      this.setupStepSpecificLogic();
    }
  }

  private getNextAvailableStep(): WizardStep {
    for (let step = WizardStep.USER_REGISTRATION; step <= WizardStep.PREVIEW; step++) {
      if (!this.completedSteps.has(step)) {
        return step;
      }
    }
    return WizardStep.PREVIEW;
  }

  // Step Validation and Data Management
  isCurrentStepValid(): boolean {
    switch (this.currentStep) {
      case WizardStep.USER_REGISTRATION:
        return this.userRegistrationForm.valid;
      case WizardStep.COMPANY_INFO:
        return this.companyInfoForm.valid;
      case WizardStep.ADDRESS_INFO:
        return this.addressInfoForm.valid;
      case WizardStep.OWNER_INFO:
        return this.ownerInfoForm.valid;
      case WizardStep.PREVIEW:
        return true;
      default:
        return false;
    }
  }

  private saveCurrentStepData(): void {
    switch (this.currentStep) {
      case WizardStep.USER_REGISTRATION:
        this.wizardData.userRegistration = this.userRegistrationForm.value;
        break;
      case WizardStep.COMPANY_INFO:
        this.wizardData.companyInfo = this.companyInfoForm.value;
        break;
      case WizardStep.ADDRESS_INFO:
        this.wizardData.addressInfo = this.addressInfoForm.value;
        break;
      case WizardStep.OWNER_INFO:
        this.wizardData.ownerInfo = this.ownerInfoForm.value;
        break;
    }
  }

  private setupStepSpecificLogic(): void {
    switch (this.currentStep) {
      case WizardStep.ADDRESS_INFO:
        this.setupTownAutocomplete();
        break;
      case WizardStep.OWNER_INFO:
        // Adım 1'den email'i otomatik doldur
        if (this.wizardData.userRegistration?.email) {
          // Email readonly olacak, sadece gösterim için
        }
        break;
      case WizardStep.PREVIEW:
        this.preparePreviewData();
        break;
    }
  }

  // Autocomplete Setup
  private setupCityAutocomplete(): void {
    this.filteredCities = this.addressInfoForm.get('city')!.valueChanges.pipe(
      startWith(''),
      map(value => typeof value === 'string' ? value : value?.cityName),
      map(name => name ? this._filterCities(name) : this.cities.slice())
    );
  }

  private setupTownAutocomplete(): void {
    // İl seçildiğinde ilçeleri yükle
    const selectedCity = this.addressInfoForm.get('city')!.value;
    if (selectedCity && selectedCity.cityID) {
      this.townService.getTownsByCityId(selectedCity.cityID).subscribe(response => {
        this.towns = response.data;
        this.filteredTowns = this.addressInfoForm.get('town')!.valueChanges.pipe(
          startWith(''),
          map(value => typeof value === 'string' ? value : value?.townName),
          map(name => name ? this._filterTowns(name) : this.towns.slice())
        );
      });
    }
  }

  private preparePreviewData(): void {
    // Tüm adımlardan gelen verileri birleştir
    // Bu metod preview adımında kullanılacak
  }

  // Final Submission
  submitUnifiedCompany(): void {
    if (!this.isAllStepsCompleted()) {
      this.toastrService.error('Lütfen tüm adımları tamamlayın', 'Hata');
      return;
    }

    this.isSubmitting = true;

    // UnifiedCompanyAddDto oluştur
    const unifiedCompanyDto: UnifiedCompanyAddDto = {
      // Adım 1: Kullanıcı bilgileri
      userFirstName: this.wizardData.userRegistration.firstName,
      userLastName: this.wizardData.userRegistration.lastName,
      userEmail: this.wizardData.userRegistration.email,
      userPassword: this.wizardData.userRegistration.password,

      // Adım 2: Şirket bilgileri
      companyName: this.wizardData.companyInfo.companyName,
      companyPhone: this.wizardData.companyInfo.companyPhone,

      // Adım 3: Adres bilgileri
      cityID: this.wizardData.addressInfo.city.cityID,
      townID: this.wizardData.addressInfo.town.townID,
      address: this.wizardData.addressInfo.address,

      // Adım 4: Salon sahibi bilgileri
      ownerFullName: this.wizardData.ownerInfo.ownerFullName,
      ownerPhone: this.wizardData.ownerInfo.ownerPhone
    };

    this.unifiedCompanyService.addUnifiedCompany(unifiedCompanyDto).subscribe(
      (response) => {
        this.toastrService.success(response.message || 'Salon başarıyla oluşturuldu', 'Başarılı');
        this.resetWizard();
        this.router.navigate(['/company-list']); // Listeleme sayfasına yönlendir
        this.isSubmitting = false;
      },
      (error) => {
        this.toastrService.error(error.error?.message || 'Salon oluşturulurken bir hata oluştu', 'Hata');
        console.error('Unified company creation error:', error);
        this.isSubmitting = false;
      }
    );
  }

  private isAllStepsCompleted(): boolean {
    return this.completedSteps.size >= 4; // İlk 4 adım tamamlanmış olmalı
  }

  private resetWizard(): void {
    this.currentStep = WizardStep.USER_REGISTRATION;
    this.completedSteps.clear();
    this.wizardData = {};
    this.initializeForms();
  }

  // Utility Methods
  getCities(): void {
    this.cityService.getCities().subscribe((response) => {
      this.cities = response.data;
    });
  }

  displayCity(city: City): string {
    return city && city.cityName ? city.cityName : '';
  }

  displayTown(town: Town): string {
    return town && town.townName ? town.townName : '';
  }
  private _filterCities(name: string): City[] {
    const filterValue = name.toLowerCase();
    return this.cities.filter(city => city.cityName.toLowerCase().includes(filterValue));
  }

  private _filterTowns(name: string): Town[] {
    const filterValue = name.toLowerCase();
    return this.towns.filter(town => town.townName.toLowerCase().includes(filterValue));
  }

  // Step Progress Helpers
  getStepTitle(step: WizardStep): string {
    switch (step) {
      case WizardStep.USER_REGISTRATION: return 'Kullanıcı Kaydı';
      case WizardStep.COMPANY_INFO: return 'Şirket Bilgileri';
      case WizardStep.ADDRESS_INFO: return 'Adres Bilgileri';
      case WizardStep.OWNER_INFO: return 'Salon Sahibi Bilgileri';
      case WizardStep.PREVIEW: return 'Önizleme & Kaydet';
      default: return '';
    }
  }

  getStepIcon(step: WizardStep): any {
    switch (step) {
      case WizardStep.USER_REGISTRATION: return this.faUser;
      case WizardStep.COMPANY_INFO: return this.faBuilding;
      case WizardStep.ADDRESS_INFO: return this.faMapMarkerAlt;
      case WizardStep.OWNER_INFO: return this.faPhone;
      case WizardStep.PREVIEW: return this.faCheck;
      default: return this.faInfoCircle;
    }
  }

  isStepCompleted(step: WizardStep): boolean {
    return this.completedSteps.has(step);
  }

  isStepActive(step: WizardStep): boolean {
    return this.currentStep === step;
  }

  isStepAccessible(step: WizardStep): boolean {
    return this.isStepCompleted(step) || step === this.getNextAvailableStep() || step === this.currentStep;
  }

}
